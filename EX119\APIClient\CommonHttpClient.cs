﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Admin;
using System.Data.SqlClient;
using System.Data;
using System.IO;
using System.Net;
using System.Text.RegularExpressions;

namespace CommonHttpClient
{
    public class CommonHttpClient1 : IDisposable
    {
        HttpClient _httpClient = new HttpClient();
        private readonly DatabaseHelper _databaseHelper;

        public CommonHttpClient1()
        {
            _databaseHelper = new DatabaseHelper();
        }

        public async Task<string> ReadDataFromCalgenie(string prodcode, string prodNme)
        {
            try
            {
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072; // Tls12 for .NET 4.0

                string apiUrl = ConfigurationManager.AppSettings["CaligenApi"];
                if (string.IsNullOrWhiteSpace(apiUrl))
                {
                    return "Caligen API URL is not configured.";
                }

                string externalApiUrl = string.Format("{0}?productName={1}&productCode={2}",
                    apiUrl, Uri.EscapeDataString(prodNme), Uri.EscapeDataString(prodcode));

                using (HttpClientHandler handler = new HttpClientHandler())
                {
                    // For .NET 4.0, we need to handle certificate validation differently
                    ServicePointManager.ServerCertificateValidationCallback =
                        (sender, certificate, chain, sslPolicyErrors) => true;

                    using (HttpClient client = new HttpClient(handler))
                    {
                        HttpResponseMessage response = await client.GetAsync(externalApiUrl);
                        if (!response.IsSuccessStatusCode)
                        {
                            return "Failed to get data from external API.";
                        }

                        string jsonString = await response.Content.ReadAsStringAsync();

                        if (string.IsNullOrWhiteSpace(jsonString) || jsonString == "null")
                        {
                            return "No data found.";
                        }

                        List<FileTestDto> testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);
                        if (testDetailsList == null || !testDetailsList.Any())
                        {
                            return "No valid test details found.";
                        }

                        // Insert data using DatabaseHelper
                        int docCnt = 1;
                        foreach (var testDetails in testDetailsList)
                        {
                            string documentNo = string.Format("{0}_{1}", testDetails.DocumentNo, docCnt);
                            bool insertResult = await _databaseHelper.InsertTestDetailsAsync(
                                documentNo,
                                testDetails.PublishedDate,
                                testDetails.TestJson);

                            if (!insertResult)
                            {
                                // Log individual insert errors
                                MaintainLog(string.Format("Error inserting document {0}", documentNo));
                            }

                            docCnt++;
                        }

                        // Call TestJSONParsing after data insertion
                        await TestJSONParsing();

                        return "Data inserted successfully.";
                    }
                }
            }
            catch (Exception ex)
            {
                return "Error: " + ex.Message;
            }
        }
      
        public string ReadMindate()
        {
            return _databaseHelper.ReadMinDate();
        }

        public async Task<string> WSReadDataFromCalgenie()
         {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                using (var handler = new HttpClientHandler())
                {
                    handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;

                    using (var httpClient = new HttpClient(handler))
                    {

                        string publishedOnMin = ReadMindate(); /*DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");*/

                        // Step 2: Build external API URL
                        string baseApiUrl = ConfigurationManager.AppSettings["CaligenWorksheetApi"];
                        if (string.IsNullOrWhiteSpace(baseApiUrl))
                            return "Error: Worksheet API URL is not configured.";

                        string worksheetApiUrl = string.Format("{0}?PublishedOnMin={1}&Sorting=publishedOn", baseApiUrl, publishedOnMin);
                        var worksheetResponse = await httpClient.GetAsync(worksheetApiUrl);
                        if (!worksheetResponse.IsSuccessStatusCode)
                            return "Error: Failed to fetch worksheet data.";

                        string jsonString = await worksheetResponse.Content.ReadAsStringAsync();
                        if (string.IsNullOrWhiteSpace(jsonString) || jsonString.Trim().Equals("null", StringComparison.OrdinalIgnoreCase))
                            return "Error: Empty response from worksheet API.";

                        var worksheetData = JsonConvert.DeserializeObject<CaligenieWorksheetResponse>(jsonString);
                        if (worksheetData == null || worksheetData.items == null || !worksheetData.items.Any())
                            return "Error: No worksheet data found.";

                        // Step 3: Process each worksheet
                        foreach (var worksheet in worksheetData.items)
                        {
                            string updatedTestJson = string.Empty;
                            if (!string.Equals(worksheet.worksheetType, "section", StringComparison.OrdinalIgnoreCase))
                            {
                                // Step 3.1: Fetch image data
                                string imgApiBase = ConfigurationManager.AppSettings["CaligenImageApiBaseUrl"];
                                if (string.IsNullOrWhiteSpace(imgApiBase))
                                    continue;

                                string imageApiUrl = string.Format("{0}?id={1}", imgApiBase, worksheet.Id);
                                var imageResponse = await httpClient.GetAsync(imageApiUrl);
                                if (!imageResponse.IsSuccessStatusCode)
                                    continue;

                                string imageJson = await imageResponse.Content.ReadAsStringAsync();
                                var imageList = JsonConvert.DeserializeObject<List<ImageApiResponse>>(imageJson);

                                // Step 3.2: Build image HTML
                                StringBuilder imageTagsBuilder = new StringBuilder();
                                if (imageList != null && imageList.Any())
                                {
                                    foreach (var img in imageList)
                                    {
                                        imageTagsBuilder.AppendFormat(
                                            "<img src=\"data:image/png;base64,{0}\" style=\"width: 500px; height: 250px;\" /><br>",
                                            img.Image);
                                    }
                                }

                                // Step 3.3: Append images to tests
                                if (string.IsNullOrWhiteSpace(worksheet.TestJson))
                                    continue;

                                var testObject = JsonConvert.DeserializeObject<Dictionary<string, List<DraftTestDetail>>>(worksheet.TestJson);
                                List<DraftTestDetail> tests;
                                if (testObject != null && testObject.TryGetValue("Tests", out tests) && tests != null)
                                {
                                    foreach (var test in tests)
                                    {
                                        test.Procedure += imageTagsBuilder.ToString();
                                    }
                                }

                                 updatedTestJson = JsonConvert.SerializeObject(testObject);
                            }
                            else
                            {
                                updatedTestJson = worksheet.TestJson;
                            }

                            var pushRequest = new PushworksheetRequest
                            {
                                DocumentNo = worksheet.DocumentNo,
                                CreatedOn = worksheet.CreatedOn,
                                TestJson = updatedTestJson,                              
                                PublishedDate = worksheet.PublishedDate,
                                JsonId = worksheet.Id,
                            };
                            int insertResult = 0;
                            // Step 3.4: Insert to database using DatabaseHelper
                            insertResult = await _databaseHelper.writeData(pushRequest, "STP_LMS_BAL_TST_DET_INF_WST", System.Data.CommandType.StoredProcedure, null);
                            await Task.Delay(10);

                            if (insertResult<0)
                            {
                                // Log worksheet insert error
                               // MaintainLog($"Error inserting worksheet {worksheet.DocumentNo}");
                                continue;
                            }
                        }

                        // After successful data insertion, call WorksheetJSONParsing
                        string parsingResult = await WorksheetJSONParsing();

                        return string.Format("Data inserted successfully. {0}", parsingResult);
                    }
                }
            }
            catch (Exception ex)
            {
                return string.Format("Error: Exception occurred - {0}", ex.Message);
            }
         }
      
        public async Task<string> WorksheetJSONParsing()
        {
            try
            {
                // Check if there are records with Status 0
                var worksheetRecords = await _databaseHelper.GetWorksheetDetailsForParsingAsync();
                if (worksheetRecords == null || !worksheetRecords.Any())
                {
                    return "No New records found or all records are already parsed.";
                }

                int successfulRecords = 0;
                int failedRecords = 0;
                int recordCounter = 1;

                foreach (var pushWorkSheetDetail in worksheetRecords)
                {
                    try
                    {
                        string TempId = string.Empty;
                        string UniqueSeqNO = string.Empty;
                        int IsMoa = 0;

                        // Check if JSON contains WSTypes - if yes, use new format processing
                        bool hasWSTypes = false;
                        try
                        {
                            var testForWSTypes = JsonConvert.DeserializeObject<Dictionary<string, List<DraftTestDetailWithWSTypes>>>(pushWorkSheetDetail.TestJson);
                            if (testForWSTypes.ContainsKey("Tests") && testForWSTypes["Tests"].Any(t => t.WSTypes != null && t.WSTypes.Any()))
                            {
                                hasWSTypes = true;
                            }
                        }
                        catch
                        {
                            hasWSTypes = false;
                        }

                        if (hasWSTypes)
                        {
                            // Process new format with WSTypes
                            var newFormatObject = JsonConvert.DeserializeObject<Dictionary<string, List<DraftTestDetailWithWSTypes>>>(pushWorkSheetDetail.TestJson);
                            List<DraftTestDetailWithWSTypes> worksheetDetailsWithWSTypes = newFormatObject["Tests"];

                            foreach (var detail in worksheetDetailsWithWSTypes)
                            {
                                string mainTestName = detail.TestName ?? null;
                                string SpecNo = detail.SpecNo ?? string.Empty;
                                if (string.IsNullOrEmpty(mainTestName))
                                {
                                    continue; // Skip if main test name is empty
                                }

                                if (detail.WSTypes != null && detail.WSTypes.Any())
                                {
                                    // Collect procedures + section names
                                    var allProcedures = new StringBuilder();
                                    var SecNmeSpecNo = string.Empty;

                                    foreach (var wsType in detail.WSTypes)
                                    {
                                        //if (!string.IsNullOrEmpty(wsType.Procedure))
                                        //{
                                        SecNmeSpecNo = !string.IsNullOrEmpty(SpecNo) ? string.Format("{0}({1})", wsType.SecName, SpecNo) : wsType.SecName;
                                        //    allProcedures.AppendLine(wsType.Procedure);
                                        //}


                                        // Retrieve TempId and UniqueSeqNO only once for each worksheet
                                        //if (IsMoa == 0)
                                        //{
                                        //    TempId = await _databaseHelper.GetWorksheetFieldCountAsync(1);
                                        //    UniqueSeqNO = await _databaseHelper.GetWorksheetUniqueSeqAsync();
                                        //}

                                        var WorksheetModel = new WorksheetModel1
                                        {
                                            WorksheetName = SecNmeSpecNo,
                                            htmdata = wsType.Procedure, // You can apply string replacements if needed
                                            JsonId = pushWorkSheetDetail.JsonId,
                                            type = 2
                                        };

                                        // Call DB once for all sections
                                        var outparams = new OutParams();
                                        var status = await _databaseHelper.writeData(WorksheetModel, "STP_LMS_BAL_WST_TRN_CLD_CALGEN", CommandType.StoredProcedure, outparams);
                                    }
                                    
                                }
                            }
                        }
                        else
                        {
                            // Process old format (existing code)
                            var rootObject = JsonConvert.DeserializeObject<Dictionary<string, List<DraftTestDetail>>>(pushWorkSheetDetail.TestJson);
                            List<DraftTestDetail> worksheetDetails = rootObject["Tests"];
                            foreach (var detail in worksheetDetails)
                            {
                                string testName = detail.TestName != null ? detail.TestName.ToString() : null;
                                string procedure = detail.Procedure != null ? detail.Procedure.ToString() : null;
                                string SpecNo = detail.SpecNo != null ? detail.SpecNo.ToString() : string.Empty;
                                string TstNmeSpecNo = string.Empty;
                                TstNmeSpecNo = !string.IsNullOrEmpty(SpecNo) ? string.Format("{0}({1})", testName, SpecNo) : testName;

                                if (string.IsNullOrEmpty(testName))
                                {
                                    continue; // Don't return from the method – just skip this detail
                                }

                                // Retrieve TempId and UniqueSeqNO only once for each worksheet
                                //if (IsMoa == 0)
                                //{
                                //    TempId = await _databaseHelper.GetWorksheetFieldCountAsync(1);
                                //    UniqueSeqNO = await _databaseHelper.GetWorksheetUniqueSeqAsync();
                                //}

                                var WorksheetModel = new WorksheetModel1
                                {
                                    WorksheetName = TstNmeSpecNo,
                                    htmdata = procedure, // You can apply string replacements if needed
                                    JsonId = pushWorkSheetDetail.JsonId,
                                    type = 1
                                };

                                // Call to the method to submit worksheet data
                                var outparams = new OutParams();
                                var status = await _databaseHelper.writeData(WorksheetModel, "STP_LMS_BAL_WST_TRN_CLD_CALGEN", CommandType.StoredProcedure, outparams); 
                            }
                        }

                        // Update the status of the current pushWorkSheetDetail entry
                        //await _databaseHelper.UpdateWorksheetStatusAsync(pushWorkSheetDetail.DocumentNo, pushWorkSheetDetail.JsonId, 1);
                        successfulRecords++;
                    }
                    catch (Exception recordEx)
                    {
                        // Log the error but continue with next record
                        failedRecords++;
                        MaintainLog(string.Format("Worksheet record {0} failed: {1}", recordCounter, recordEx.Message));
                        // Continue to next record - don't break the loop
                    }
                    finally
                    {
                        recordCounter++;
                    }
                }

                // Return success message with statistics
                string resultMessage = string.Format("Worksheet processing completed. Total records: {0}, Successful: {1}, Failed: {2}",
                    worksheetRecords.Count, successfulRecords, failedRecords);

                // Only log if there were errors
                if (failedRecords > 0)
                {
                    MaintainLog(resultMessage);
                }

                return resultMessage;
            }
            catch (Exception ex)
            {
                MaintainLog(string.Format("Error in WorksheetJSONParsing: {0}", ex.Message));
                return "An error occurred while fetching the details.";
            }
        }

         public async Task<string> TestJSONParsing()
        {
            try
            {
                // Retrieve the record from the database using DatabaseHelper
                List<PushTestDetails> pushTestDetails;
                try
                {
                    pushTestDetails = await _databaseHelper.GetTestDetailsForParsingAsync();
                }
                catch (Exception dbEx)
                {
                    MaintainLog(string.Format("Database error in GetTestDetailsForParsingAsync: {0}", dbEx.Message));
                    return string.Format("Database error: {0}", dbEx.Message);
                }

                if (pushTestDetails == null || pushTestDetails.Count == 0)
                {
                    return "Record not found or Record already Parsed.";
                }

                int testCodeCounter = 1;
                int successfulRecords = 0;
                int failedRecords = 0;

                foreach (var item in pushTestDetails)
                {
                    try
                    {
                        // Join all test names into a comma-separated string
                        string FinalOutput = "";

                        // Deserialize TestJson into a dynamic object
                        dynamic testDetails;
                        Root root;

                        try
                        {
                            testDetails = JsonConvert.DeserializeObject<dynamic>(item.TestJson);
                            root = JsonConvert.DeserializeObject<Root>(item.TestJson);
                        }
                        catch (Exception jsonEx)
                        {
                            MaintainLog(string.Format("Record {0} - JSON deserialization failed: {1}", testCodeCounter, jsonEx.Message));
                            failedRecords++;
                            testCodeCounter++;
                            continue; // Skip to next record
                        }
                       
                        string productName = "";
                        string productCode = "";
                        string materialType = "";
                        DateTime testcodeDyn = item.CreatedOn;
                        string uniqueCode = testcodeDyn.ToString("ddMMyyHHmmss");
                        //string uniqueCode = DateTime.UtcNow.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);                       


                        //Extract product details
                        try
                        {
                            productName = testDetails.ProductTypeDetails.Product;
                            productCode = testDetails.ProductTypeDetails.ProductCode;
                            materialType = testDetails.ProductTypeDetails.MaterialType;
                        }
                        catch (Exception)
                        {
                            productName = testDetails.ProductDetails.ProductName;
                            productCode = testDetails.ProductDetails.ProductCode;
                            materialType = testDetails.ProductDetails.MaterialType;
                        }




                        // Execute the stored procedure using DatabaseHelper
                        var PrdSpec = await _databaseHelper.GetProductSpecificationAsync(productCode, materialType);

                        if (PrdSpec == null)
                        {
                            MaintainLog(string.Format("Product specification not found for ProductCode: {0}, MaterialType: {1}", productCode, materialType));
                            continue;
                        }

                        // Retrieve the output values
                        int specificationId = (int)PrdSpec.SPecificationId;
                        int productId = (int)PrdSpec.ProductId;

                        var testNames = new List<string>();
                        if (specificationId != 0)
                        {
                            
                            string LODCOAtxt = "";
                            string LOQCOAtxt = "";                                                   

                            foreach (var test in root.Tests)
                            {
                                if (test.TestType == "Quantitative" || test.TestType == "Quantitative-2" || test.TestType== "Multi-Quantitative")
                                {
                                    // Initialize lists to store variable details, symbols, UOMs, subtest names, formulas, and formula UOMs
                                    List<string> variableDetails = new List<string>();
                                    List<string> symbolDetails = new List<string>();
                                    List<string> uomDetails = new List<string>();
                                    List<string> subTestNameDetails = new List<string>();
                                    List<string> MethodNoDetails = new List<string>();
                                    List<string> formulaDetails = new List<string>();
                                    List<string> formulaUOMDetails = new List<string>();
                                    List<string> NoofDecimalDetails = new List<string>();
                                    List<string> NoofDecimalLODQDetails = new List<string>();
                                    List<string> LODDetails = new List<string>();
                                    List<string> LOQDetails = new List<string>();
                                    List<string> PassLimitDetails = new List<string>();
                                    List<string> PassLimitDescDetails = new List<string>();
                                    List<string> AlertLimitsDetails = new List<string>();
                                    List<string> ActionLimitsDetails = new List<string>();


                                    // Variables handling
                                    string variablesString = string.Empty;
                                    string symbolsString = string.Empty;
                                    string uomsString = string.Empty;

                                    string spvariablesString = string.Empty;
                                    string spsymbolsString = string.Empty;
                                    string spuomsString = string.Empty;
                                    string spInputExpressionString = string.Empty;
                                    string spLowerLimitString = string.Empty;
                                    string spUpperLimitString = string.Empty;
                                    string spDecimalString = string.Empty;
                                    string spSpTypeString = string.Empty;
                                    string spDisplayinCoaString = string.Empty;
                                    LOQCOAtxt = "";
                                    LODCOAtxt = "";
                                    if (test.Variables != null)
                                    {

                                        foreach (var variable in test.Variables)
                                        {

                                            variableDetails.Add((string)variable.VariableName == "N/A" ? string.Empty : (string)variable.VariableName ?? string.Empty);
                                            symbolDetails.Add(double.TryParse((string)variable.Symbol, out _) || (string)variable.Symbol == "N/A" ? string.Empty : (string)variable.Symbol ?? string.Empty);
                                            uomDetails.Add((string)variable.UnitOfMeasurement);
                                        }

                                        // Join the variables, symbols, and UOMs using the custom delimiter #*#
                                        variablesString = string.Join("#*#", variableDetails);
                                        symbolsString = string.Join("#*#", symbolDetails);
                                        uomsString = string.Join("#*#", uomDetails);
                                    }
                                    if (test.SpecialVariables != null)
                                    {
                                        if (test.SpecialVariables.Count > 0)
                                        {
                                            List<string> spvariableDetails = new List<string>();
                                            List<string> spsymbolDetails = new List<string>();
                                            List<string> spuomDetails = new List<string>();
                                            List<string> spInputExpressionDetails = new List<string>();
                                            List<string> spUpperLimitDetails = new List<string>();
                                            List<string> spLowerLimitDetails = new List<string>();
                                            List<string> spDecimalDetails = new List<string>();
                                            List<string> spDisplayinCoaDetails = new List<string>();
                                            List<string> spfnTypeDetails = new List<string>();


                                            foreach (var spvariable in test.SpecialVariables)
                                            {
                                                spvariableDetails.Add((string)spvariable.VariableName == "N/A" ? string.Empty : (string)spvariable.VariableName ?? string.Empty);
                                                spsymbolDetails.Add(double.TryParse((string)spvariable.Symbol, out _) || (string)spvariable.Symbol == "N/A" ? string.Empty : (string)spvariable.Symbol ?? string.Empty);
                                                spuomDetails.Add((string)spvariable.UOM);
                                                spInputExpressionDetails.Add(string.IsNullOrEmpty((string)spvariable.Formula) || (string)spvariable.Formula == "N/A" ? string.Empty : (string)spvariable.Formula.Trim());

                                                spLowerLimitDetails.Add(string.IsNullOrEmpty((string)spvariable.LowerLimit) || (string)spvariable.LowerLimit == "N/A"
                                                                       ? string.Empty : (string)spvariable.LowerLimit);

                                                spUpperLimitDetails.Add(string.IsNullOrEmpty((string)spvariable.UpperLimit) || (string)spvariable.UpperLimit == "N/A"
                                                                       ? string.Empty : (string)spvariable.UpperLimit);
                                                spDecimalDetails.Add(string.IsNullOrEmpty((string)spvariable.Decimals) || (string)spvariable.Decimals == "N/A"
                                                                        ? string.Empty : (string)spvariable.Decimals);
                                                spDisplayinCoaDetails.Add(string.IsNullOrEmpty((string)spvariable.DisplayinCOA) || (string)spvariable.DisplayinCOA == "N/A"
                                                                       ? string.Empty : (string)spvariable.DisplayinCOA == "false" ? "0" : "1");

                                                string functionTypeCode;
                                                string functionType = (string)spvariable.FunctionType;
                                                switch (functionType)
                                                {
                                                    case null:
                                                    case "":
                                                    case "N/A":
                                                    case "Formula":
                                                        functionTypeCode = "9";
                                                        break;
                                                    case "Max":
                                                        functionTypeCode = "0";
                                                        break;
                                                    case "Min":
                                                        functionTypeCode = "1";
                                                        break;
                                                    case "Avg":
                                                        functionTypeCode = "2";
                                                        break;
                                                    case "SD":
                                                        functionTypeCode = "3";
                                                        break;
                                                    case "RSD":
                                                        functionTypeCode = "4";
                                                        break;
                                                    case "Sin":
                                                        functionTypeCode = "5";
                                                        break;
                                                    case "Cos":
                                                        functionTypeCode = "6";
                                                        break;
                                                    case "Tan":
                                                        functionTypeCode = "7";
                                                        break;
                                                    case "Log":
                                                        functionTypeCode = "8";
                                                        break;
                                                    case "Abs":
                                                        functionTypeCode = "10";
                                                        break;
                                                    case "Slope":
                                                        functionTypeCode = "11";
                                                        break;
                                                    case "Linearity":
                                                        functionTypeCode = "12";
                                                        break;
                                                    case "Correlation":
                                                        functionTypeCode = "13";
                                                        break;
                                                    default:
                                                        functionTypeCode = "9"; // Default to Formula
                                                        break;
                                                }
                                                spfnTypeDetails.Add(functionTypeCode);


                                            }
                                            spvariablesString = string.Join("#*#", spvariableDetails);
                                            spsymbolsString = string.Join("#*#", spsymbolDetails);
                                            spuomsString = string.Join("#*#", spuomDetails);
                                            spSpTypeString = string.Join(",", spfnTypeDetails);
                                            spInputExpressionString = string.Join("#*#", spInputExpressionDetails);
                                            spDecimalString = string.Join(",", spDecimalDetails);
                                            spLowerLimitString = string.Join(",", spLowerLimitDetails);
                                            spUpperLimitString = string.Join(",", spUpperLimitDetails);
                                            spDisplayinCoaString = string.Join(",", spDisplayinCoaDetails);

                                        }
                                    }
                                    // SubTests 
                                    string subTestNameString = string.Empty;
                                    string MethodNoString = string.Empty;
                                    string formulaString = string.Empty;
                                    string formulaUOMString = string.Empty;
                                    string NoofDecimalString = string.Empty;
                                    string PasslimitString = string.Empty;
                                    string LOD = string.Empty;
                                    string LOQ = string.Empty;
                                    string AlertLimits = string.Empty;
                                    string ActionLimits = string.Empty;
                                    string NoofDecimalLODQString = string.Empty;
                                    string PassLimitDescString = string.Empty;
                                    string Conpasslimit = string.Empty;
                                    int Cpasslimit = 0;
                                    int tstcnt = 0;

                                    if (!string.IsNullOrWhiteSpace(test.PassLimits))
                                    {
                                        Conpasslimit = (test.PassLimits ?? "");
                                        //(Conpasslimit) += new string(',', 4 - (Conpasslimit).Count(c => c == ','));
                                        int commaCount = Conpasslimit.Count(c => c == ',');
                                        if (commaCount < 4)
                                        {
                                            Conpasslimit += new string(',', 4 - commaCount);
                                        }

                                    }

                                    if (test.subtests != null && test.subtests.Any())
                                    {
                                        tstcnt = 0;
                                        foreach (var Subtest in test.subtests)
                                        {
                                            if (tstcnt == 0)
                                            {                                              
                                                LODCOAtxt = "";
                                                LOQCOAtxt = "";                                            
                                                
                                            }
                                            else
                                            {                                                
                                                LOQCOAtxt += "#*#" + " ";
                                                LODCOAtxt += "#*#" + " ";                                               
                                            }
                                            tstcnt++;
                                        }
                                    }


                                    if (test.subtests != null)

                                    {
                                        foreach (var subTest in test.subtests)
                                        {
                                            subTestNameDetails.Add((string)subTest.SubTestName == "N/A" ? string.Empty : (string)subTest.SubTestName ?? string.Empty);
                                            MethodNoDetails.Add((string)subTest.MethodNo == "N/A" ? string.Empty : (string)subTest.MethodNo ?? string.Empty);
                                            formulaDetails.Add((string)subTest.Formula == "N/A" ? string.Empty : (string)subTest.Formula ?? string.Empty);
                                            formulaUOMDetails.Add((string)subTest.UOM ?? string.Empty);
                                            NoofDecimalDetails.Add((string)subTest.NoOfDecimals ?? string.Empty);
                                            NoofDecimalLODQDetails.Add((string)subTest.NumberOfDecimalsForLODLOQ ?? string.Empty);
                                            PassLimitDetails.Add((string)subTest.PassLimits ?? string.Empty);
                                            LODDetails.Add((string)subTest.LimitOfDetection == "N/A" ? string.Empty : (string)subTest.LimitOfDetection ?? string.Empty);
                                            LOQDetails.Add((string)subTest.LimitOfQuantification == "N/A" ? string.Empty : (string)subTest.LimitOfQuantification ?? string.Empty);
                                            PassLimitDescDetails.Add((string)subTest.PassLimitDescription == "N/A" ? string.Empty : (string)subTest.PassLimitDescription ?? string.Empty);
                                            AlertLimitsDetails.Add((string)subTest.LimitOfAlert == "N/A" ? string.Empty : (string)subTest.LimitOfAlert ?? string.Empty);
                                            ActionLimitsDetails.Add((string)subTest.LimitOfAction == "N/A" ? string.Empty : (string)subTest.LimitOfAction ?? string.Empty);
                                        }

                                        // Join the subtest names, formulas, and formula UOMs using the custom delimiter #*#
                                        subTestNameString = string.Join("#*#", subTestNameDetails);
                                        MethodNoString = string.Join("#*#", MethodNoDetails);
                                        formulaString = string.Join("#*#", formulaDetails);
                                        formulaUOMString = string.Join("#*#", formulaUOMDetails);
                                        NoofDecimalString = string.Join("#*#", NoofDecimalDetails);
                                        NoofDecimalLODQString = string.Join(",", NoofDecimalLODQDetails);
                                        PasslimitString = string.Join("#*#", PassLimitDetails);
                                        LOD = string.Join(",", LODDetails);
                                        LOQ = string.Join(",", LOQDetails);
                                        PassLimitDescString = string.Join("#*#", PassLimitDescDetails);
                                        AlertLimits = string.Join(",", AlertLimitsDetails);
                                        ActionLimits = string.Join(",", ActionLimitsDetails);

                                    }
                                    else
                                    {
                                        subTestNameString = string.Empty;
                                        MethodNoString = string.Empty;
                                        formulaString = test.Formula == "N/A" ? string.Empty : test.Formula;
                                        formulaUOMString = test.UOM;
                                        NoofDecimalString = test.NoOfDecimals;
                                        PasslimitString = Conpasslimit;
                                        PassLimitDescString = test.PassLimitDescription == "N/A" ? string.Empty : test.PassLimitDescription;
                                        LOD = test.LimitOfDetection == "N/A" ? string.Empty : test.LimitOfDetection;
                                        LOQ = test.LimitOfQuantification == "N/A" ? string.Empty : test.LimitOfQuantification;
                                        NoofDecimalLODQString = test.NumberOfDecimalsForLODLOQ == "N/A" ? string.Empty : test.NumberOfDecimalsForLODLOQ;
                                        AlertLimits = test.LimitOfAlert == "N/A" ? string.Empty : test.LimitOfAlert;
                                        ActionLimits = test.LimitOfAction == "N/A" ? string.Empty : test.LimitOfAction;
                                    }                                  

                                    var Tstcldmodel = new TestCLDModel
                                    {
                                        ProductId = productId,
                                        TestDesc = test.TestName,
                                        SpecificationId = specificationId,
                                        Variables = variablesString,
                                        Symbols = symbolsString,
                                        Units = uomsString,
                                        SubTests = subTestNameString,
                                        Formula = string.IsNullOrWhiteSpace(formulaString) ? string.Empty : Regex.Replace(Regex.Replace(formulaString, @"(?<=\))\s*x\s*(?=\()", "*"), @"(\d+)\^(\d+)", "Math.Pow(($1), ($2))"),                                                                            
                                        FormulaUnits = formulaUOMString,
                                        PassLimits = PasslimitString,
                                        NoOfDecList = NoofDecimalString,
                                        NoOfDec = test.NoOfDecimals ?? "",
                                        LOQCOATxt = LOQCOAtxt,
                                        LODCOATxt = LODCOAtxt,
                                        LoqLimits = LOQ,
                                        LodLimits = LOD,
                                        Txt_COA = (test.FinalResultDescription == null || !test.FinalResultDescription.Any()) ? string.Empty : (test.FinalResultDescription == "N/A" ? string.Empty : test.FinalResultDescription),
                                        MethodNoList = MethodNoString,
                                        MethodNo = test.MethodNo=="N/A" ? "" : test.MethodNo ?? "",
                                        LOActionCOATxt = null,
                                        LOAlertCOATxt = null,
                                        AlertLimits = AlertLimits,
                                        ActionLimits = ActionLimits,
                                        LODDecimals = NoofDecimalLODQString,
                                        Description = PassLimitDescString,
                                        SP_Variables = string.IsNullOrWhiteSpace(spvariablesString) ? string.Empty : spvariablesString,
                                        SP_Symbols = string.IsNullOrWhiteSpace(spsymbolsString) ? string.Empty : spsymbolsString,
                                        SP_Units = string.IsNullOrWhiteSpace(spuomsString) ? string.Empty : spuomsString,
                                        SP_Types = string.IsNullOrWhiteSpace(spSpTypeString) ? string.Empty : spSpTypeString,
                                        SP_InputExp = (string.IsNullOrWhiteSpace(spInputExpressionString) ? string.Empty : Regex.Replace(Regex.Replace(spInputExpressionString, @"(?<=\))\s*x\s*(?=\()", "*"), @"(\d+)\^(\d+)", "Math.Pow(($1), ($2))")),
                                        SP_NoOfDec = string.IsNullOrWhiteSpace(spDecimalString) ? string.Empty : spDecimalString,
                                        SP_LL = string.IsNullOrWhiteSpace(spLowerLimitString) ? string.Empty : spLowerLimitString,
                                        SP_UL = string.IsNullOrWhiteSpace(spUpperLimitString) ? string.Empty : spUpperLimitString,
                                        SP_COA = string.IsNullOrWhiteSpace(spDisplayinCoaString) ? string.Empty : spDisplayinCoaString,
                                        TestCode = string.Format("Qn{0:000}{1}", testCodeCounter, uniqueCode),
                                        TestCategoryId = 0,
                                        TchTypeId = 0,
                                        TestType = test.TestType == "Quantitative-2" ? 18 : ((test.subtests == null || !test.subtests.Any()) ? 1 : 16),
                                        TransCode = string.Format("Qn{0:000}", testCodeCounter),
                                        DocId = item.Id
                                    };

                                    testCodeCounter++;
                                    var outparams = new OutParams();
                                    var status = await _databaseHelper.writeData(Tstcldmodel, "STP_LMS_BAL_TST_TRN_CLD", CommandType.StoredProcedure, outparams);
                                }

                                if (test.TestType == "Qualitative" || test.TestType== "Multi-Qualitative")
                                {

                                    // Initialize lists to store variable details, symbols, UOMs, subtest names, formulas, and formula UOMs

                                    List<string> passStatusDetails = new List<string>();


                                    List<string> subTestNameDetails = new List<string>();
                                    List<string> MethodNoDetails = new List<string>();
                                    List<string> allSubtestDescriptions = new List<string>();
                                    List<string> allPassStatusDetails = new List<string>();
                                    List<string> PassLimitDescriptionDetails = new List<string>();
                                    List<string> SpecificationDescription = new List<string>();

                                    string subTestNameString = string.Empty;
                                    string MethodNoString = string.Empty;
                                    string variableString = string.Empty;
                                    string passStatusString = string.Empty;
                                    string PassLimitDescriptionString = string.Empty;
                                    string SpecificationDescriptionString = string.Empty;

                                    if (test.subtests != null)
                                    {
                                        foreach (var subTest in test.subtests)
                                        {
                                            List<string> variableDescriptions = new List<string>();
                                            List<string> passStatusList = new List<string>();

                                            // Add SubTestName and MethodNo details with null and "N/A" checks
                                            subTestNameDetails.Add(subTest.SubTestName == "N/A" ? string.Empty : subTest.SubTestName ?? string.Empty);
                                            MethodNoDetails.Add(subTest.MethodNo == "N/A" ? string.Empty : subTest.MethodNo ?? string.Empty);
                                            if (test.subtests != null)
                                            {
                                                if (subTest.Variables != null)
                                                {
                                                    // Loop through each variable in the current subTest
                                                    foreach (var variable in subTest.Variables)
                                                    {
                                                        // Collect descriptions and pass status with null and "N/A" checks
                                                        variableDescriptions.Add(variable.Description == "N/A" ? string.Empty : variable.Description ?? string.Empty);
                                                        passStatusList.Add(variable.PassStatus == "Pass" ? "1" : "0");
                                                    }
                                                }
                                            }

                                            // Join descriptions and pass statuses for the current subtest with `#$#`
                                            allSubtestDescriptions.Add(string.Join("#$#", variableDescriptions));
                                            allPassStatusDetails.Add(string.Join(",", passStatusList));
                                            PassLimitDescriptionDetails.Add(subTest.PassLimitDescription == "N/A" ? string.Empty : subTest.PassLimitDescription ?? string.Empty);
                                            SpecificationDescription.Add(subTest.SpecificationDescription == "N/A" ? string.Empty : subTest.SpecificationDescription ?? string.Empty);
                                        }

                                        // Join each subtest’s descriptions and pass statuses with `#*#`
                                        variableString = string.Join("#*#", allSubtestDescriptions);
                                        passStatusString = string.Join("#*#", allPassStatusDetails);
                                        subTestNameString = string.Join("#*#", subTestNameDetails);
                                        MethodNoString = string.Join("#*#", MethodNoDetails);
                                        PassLimitDescriptionString = string.Join("#*#", PassLimitDescriptionDetails);
                                        SpecificationDescriptionString = string.Join("#*#", SpecificationDescription);

                                    }

                                    else
                                    {
                                        if (test.Variables != null)
                                        {
                                            List<string> variableDescriptions = new List<string>();
                                            foreach (var variable in test.Variables)
                                            {
                                                variableDescriptions.Add(variable.Description == "N/A" ? string.Empty : variable.Description ?? string.Empty);
                                                passStatusDetails.Add((string)variable.PassStatus == "Pass" ? "1" : "0");
                                            }
                                            variableString = string.Join("#*#", variableDescriptions);
                                            passStatusString = string.Join(",", passStatusDetails);
                                            PassLimitDescriptionString = string.IsNullOrEmpty(test.PassLimitDescription) ? string.Empty : test.PassLimitDescription;
                                            SpecificationDescriptionString = string.IsNullOrEmpty(test.SpecificationDescription) ? string.Empty : test.SpecificationDescription;
                                        }

                                    }

                                    var testModel = new TestCLDModel
                                    {
                                        ProductId = productId,
                                        TestDesc = test.TestName,
                                        Description = ((PassLimitDescriptionString != null ? PassLimitDescriptionString.Length : 0) >= (SpecificationDescriptionString != null ? SpecificationDescriptionString.Length : 0)) ? PassLimitDescriptionString : SpecificationDescriptionString,
                                        SubTests = subTestNameString,
                                        SpecificationId = specificationId,                                        
                                        Variables   = variableString,                                      
                                        PassLimits = passStatusString,                                                                               
                                        MethodNo = test.MethodNo== "N/A" ? "" : test.MethodNo ?? "",
                                        MethodNoList = MethodNoString,
                                        TestCategoryId = 0,
                                        TchTypeId = 0,
                                        TestType = test.TestType== "Multi-Qualitative" ? 15 : 0,                                     
                                        TestCode = string.Format("Ql{0:000}{1}", testCodeCounter, uniqueCode), // Generate TestCode for QualitativeTests
                                        DocId = item.Id
                                    };
                                    testCodeCounter++;
                                    var outparams = new OutParams();
                                    var status = await _databaseHelper.writeData(testModel, "STP_LMS_BAL_TST_TRN_CLD", CommandType.StoredProcedure, outparams);
                                }

                                if (test.TestType == "Statistical-1" || test.TestType== "Statistical-2")
                                {
                                   
                                    string MaxLimit = "";
                                    int IsMin = 0;
                                    int IsMax = 0;
                                    int IsAvg = 0;
                                    int IsSd = 0;
                                    int IsRSD = 0;
                                    string MinLimit = "";
                                    string AvgLLimit = "";
                                    string AvgULimit = "";
                                    string SDLimit = "";
                                    string RSDLimit = "";

                                    foreach (var cal in test.Calculations)
                                    {
                                        if (cal.Calculation == "Minimum" && cal.Limit != "N/A")
                                        {
                                            IsMin = 1;
                                            MinLimit = cal.Limit ?? "";
                                        }
                                        if (cal.Calculation == "Maximum" && cal.Limit != "N/A")
                                        {
                                            IsMax = 1;
                                            MaxLimit = cal.Limit ?? "";
                                        }
                                        if (cal.Calculation == "Average")
                                        {

                                            if ((cal.AvgLimits.Select(c => c.LLimit).FirstOrDefault() != "N/A") || (cal.AvgLimits.Select(c => c.ULimit).FirstOrDefault() != "N/A"))
                                            {
                                                IsAvg = 1;
                                                AvgLLimit = cal.AvgLimits.Select(c => c.LLimit).FirstOrDefault();
                                                AvgULimit = cal.AvgLimits.Select(c => c.ULimit).FirstOrDefault();
                                            }

                                        }
                                        if (cal.Calculation == "Standard Dev" && cal.Limit != "N/A")
                                        {
                                            IsSd = 1;
                                            SDLimit = cal.Limit ?? "";
                                        }
                                        if (cal.Calculation == "Relative Std. Dev" && cal.Limit != "N/A")
                                        {
                                            IsRSD = 1;
                                            RSDLimit = cal.Limit;
                                        }

                                    }
                                    // Variables handling
                                    string variablesString = string.Empty;
                                    string symbolsString = string.Empty;
                                    string uomsString = string.Empty;
                                    List<string> variableDetails = new List<string>();
                                    List<string> symbolDetails = new List<string>();
                                    List<string> uomDetails = new List<string>();
                                    if (test.Variables != null)
                                    {
                                        foreach (var variable in test.Variables)
                                        {
                                            variableDetails.Add((string)variable.VariableName == "N/A" ? string.Empty : (string)variable.VariableName ?? string.Empty);
                                            symbolDetails.Add(double.TryParse((string)variable.Symbol, out _) || (string)variable.Symbol == "N/A" ? string.Empty : (string)variable.Symbol ?? string.Empty);
                                            uomDetails.Add((string)variable.UnitOfMeasurement == "N/A" ? string.Empty : (string)variable.UnitOfMeasurement ?? string.Empty);
                                        }

                                        // Join the variables, symbols, and UOMs using the custom delimiter #*#
                                        variablesString = string.Join("#*#", variableDetails);
                                        symbolsString = string.Join("#*#", symbolDetails);
                                        uomsString = string.Join("#*#", uomDetails);
                                    }

                                    string NMT = string.Empty;
                                    string NMT1 = string.Empty;
                                    string NRSHD = string.Empty;
                                    string LC = string.Empty;
                                    string MNVLC = string.Empty;
                                    string MXVLC = string.Empty;
                                    string StatPassLimitDescription = string.Empty;
                                    string[] NMTArray = { };

                                    if (test.AdditionalParameters != null)
                                    {
                                        foreach (var AdditionalParameters in test.AdditionalParameters)
                                        {
                                            NMTArray = (AdditionalParameters.NMT != null && AdditionalParameters.NMT.EndsWith(",") ? AdditionalParameters.NMT : string.Format("{0},", AdditionalParameters.NMT ?? "")).Split(',');
                                            NRSHD = AdditionalParameters.NRSHD == "N/A" ? string.Empty : AdditionalParameters.NRSHD;
                                            LC = AdditionalParameters.LabelClaim == "N/A" ? string.Empty : AdditionalParameters.LabelClaim;
                                            MNVLC = AdditionalParameters.MNVLC == "N/A" ? string.Empty : AdditionalParameters.MNVLC;
                                            MXVLC = AdditionalParameters.MXVLC == "N/A" ? string.Empty : AdditionalParameters.MXVLC;
                                            StatPassLimitDescription = AdditionalParameters.PassLimitDescription == "N/A" ? string.Empty : AdditionalParameters.PassLimitDescription;
                                        }

                                        if (NMTArray.Length > 0)
                                        {
                                            NMT = NMTArray[0] == "N/A" ? string.Empty : NMTArray[0];
                                            NMT1 = NMTArray[1] == "N/A" ? string.Empty : NMTArray[1];
                                        }

                                    }

                                    var testModel = new TestCLDModel
                                    {
                                        ProductId = productId,
                                        TestDesc = test.TestName,
                                        Description = StatPassLimitDescription,
                                        SpecificationId = specificationId,
                                        MethodNo = test.MethodNo,
                                        NumberOfReadings = int.TryParse(test.NumberofReadings, out int result) ? result : 0,
                                        Readings = test.DescriptionOfReading,
                                        ReadingUnits = test.UOM,
                                        IsMax = ((byte)IsMax),
                                        IsMin = ((byte)IsMin),
                                        IsAvg = ((byte)IsAvg),
                                        IsSD = ((byte)IsSd),
                                        IsRSD = ((byte)IsRSD),
                                        MinLimit = string.IsNullOrWhiteSpace(MinLimit) ? string.Empty : MinLimit,
                                        MaxLimit = string.IsNullOrWhiteSpace(MaxLimit) ? string.Empty : MaxLimit,
                                        AvgLLimit = string.IsNullOrWhiteSpace(AvgLLimit) ? string.Empty : AvgLLimit,
                                        AvgULimit = string.IsNullOrWhiteSpace(AvgULimit) ? string.Empty : AvgULimit,
                                        SDLimit = string.IsNullOrWhiteSpace(SDLimit) ? string.Empty : SDLimit,
                                        RSDLimit = string.IsNullOrWhiteSpace(RSDLimit) ? string.Empty : RSDLimit,
                                        NMTLimit = string.IsNullOrWhiteSpace(NMT) ? string.Empty : NMT,
                                        NMT_ValueLimit = string.IsNullOrWhiteSpace(NMT1) ? string.Empty : NMT1,
                                        LabelClaim = string.IsNullOrWhiteSpace(LC) ? string.Empty : LC,
                                        LCMaxLimit = string.IsNullOrWhiteSpace(MXVLC) ? string.Empty : MXVLC,
                                        LCMinLimit = string.IsNullOrWhiteSpace(MNVLC) ? string.Empty : MNVLC,
                                        LCDevAvgLimit = string.IsNullOrWhiteSpace(NRSHD) ? string.Empty : NRSHD,
                                        NoOfDec =test.NoOfDecimals ?? "",
                                        Variables= variablesString,
                                        Symbols = symbolsString,
                                        Units = uomsString,
                                        Formula = string.IsNullOrEmpty(test != null ? test.Formula : null) ? string.Empty : Regex.Replace(Regex.Replace(test.Formula, @"(?<=\))\s*x\s*(?=\()", "*"), @"(\d+)\^(\d+)", "Math.Pow(($1), ($2))"),
                                        TestCategoryId = 0,
                                        TchTypeId = 0,
                                        TestType = test.TestType== "Statistical-1" ? 5 : 10,
                                        TestCode = string.Format("Ql{0:000}{1}", testCodeCounter, uniqueCode), // Generate TestCode for QualitativeTests
                                        DocId = item.Id
                                    };
                                    testCodeCounter++;
                                    var outparams = new OutParams();
                                    var status = await _databaseHelper.writeData(testModel, "STP_LMS_BAL_TST_TRN_CLD", CommandType.StoredProcedure, outparams);
                                }                               

                            }
                        } // Close if (specificationId != 0)
                        else
                        {
                            MaintainLog("Product specification not found for ProductCode");
                        }

                        // If we reach here, the record was processed successfully
                        successfulRecords++;
                    }
                    catch (Exception recordEx)
                    {
                        // Log the error but continue with next record
                        failedRecords++;
                        MaintainLog(string.Format("Record {0} failed: {1}", testCodeCounter, recordEx.Message));
                        MaintainLog(string.Format("Record {0} stack trace: {1}", testCodeCounter, recordEx.StackTrace));
                        // Continue to next record - don't break the loop
                    }
                    finally
                    {
                        testCodeCounter++;
                    }
                } // Close foreach (var item in pushTestDetails)

                // Return success message with statistics
                string resultMessage = string.Format("Processing completed. Total records: {0}, Successful: {1}, Failed: {2}",
                    pushTestDetails.Count, successfulRecords, failedRecords);

                // Only log if there were errors
                if (failedRecords > 0)
                {
                    MaintainLog(resultMessage);
                }

                return resultMessage;
            } // Close try block
            catch
            {
                MaintainLog("Error in TestJSONParsing: An error occurred while processing the test details.");
                return "An error occurred while processing the test details.";
            }
        }

        public void Dispose()
        {
            _httpClient.Dispose();
        }

        public void MaintainLog(string LogTxt)
        {
            string path = HttpContext.Current.Server.MapPath("~/Error_Log_File");
            string logContent = "";

            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }

                string logFilePath = Path.Combine(path, "ErrorLog.txt");

                if (File.Exists(logFilePath))
                {
                    logContent = File.ReadAllText(logFilePath);
                    logContent += LogTxt + " At::" + DateTime.Now.ToString() + Environment.NewLine;
                    File.WriteAllText(logFilePath, logContent);
                }
                else
                {
                    File.WriteAllText(logFilePath, LogTxt);
                }
            }
            catch (Exception ex)
            {
                string logFilePath = Path.Combine(path, "ErrorLog.txt");

                if (File.Exists(logFilePath))
                {
                    logContent = File.ReadAllText(logFilePath);
                }

                logContent += LogTxt + " At::" + DateTime.Now.ToString() + " " + ex.Message + Environment.NewLine;
                File.WriteAllText(logFilePath, logContent);
            }
            finally
            {
                logContent = "";
            }
        }



    }
}
