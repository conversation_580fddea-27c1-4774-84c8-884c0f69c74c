﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Dapper;

namespace CommonHttpClient
{
    public class DatabaseHelper
    {
        private readonly string _connectionString;

        public DatabaseHelper()
        {
            // Read connection string from appSettings in web.config
            _connectionString = ConfigurationManager.AppSettings["MyConStr"];

            // Debug: Check if connection string is loaded
            if (string.IsNullOrEmpty(_connectionString))
            {
                System.Diagnostics.Debug.WriteLine("Warning: Connection string 'MyConStr' not found in appSettings");
            }
        }

        public DatabaseHelper(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// Insert test details using stored procedure
        /// </summary>
        public async Task<bool> InsertTestDetailsAsync(string documentNo, string createdOn, string testJson)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("STP_LMS_BAL_TST_DET_INF", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        
                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@DocumentNo", documentNo);
                        cmd.Parameters.AddWithValue("@CreatedOn", createdOn);
                        cmd.Parameters.AddWithValue("@TestJson", testJson);
                        cmd.Parameters.AddWithValue("@Status", 0);

                        conn.Open();
                        await cmd.ExecuteNonQueryAsync();
                        return true;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Read minimum date using stored procedure
        /// </summary>
        public string ReadMinDate()
        {
            string datestr = string.Empty;

            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("STP_red_min_dat", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            DataSet ds = new DataSet();
                            adapter.Fill(ds);

                            if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                            {
                                datestr = ds.Tables[0].Rows[0][0].ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                datestr = string.Empty;
            }

            return datestr;
        }

        /// <summary>
        /// Insert worksheet details using stored procedure
        /// </summary>
        public async Task<bool> InsertWorksheetDetailsAsync(string documentNo, string createdOn, string testJson, int isMOA, string publishedDate)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("STP_InsertWorksheetDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        
                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@DocumentNo", documentNo);
                        cmd.Parameters.AddWithValue("@CreatedOn", createdOn);
                        cmd.Parameters.AddWithValue("@TestJson", testJson);
                        cmd.Parameters.AddWithValue("@ISMOA", isMOA);
                        cmd.Parameters.AddWithValue("@PublishedDate", publishedDate);
                        
                        conn.Open();
                        await cmd.ExecuteNonQueryAsync();
                        return true;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Execute a generic stored procedure with parameters
        /// </summary>
        public async Task<bool> ExecuteStoredProcedureAsync(string procedureName, params SqlParameter[] parameters)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(procedureName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        if (parameters != null)
                        {
                            cmd.Parameters.AddRange(parameters);
                        }

                        conn.Open();
                        await cmd.ExecuteNonQueryAsync();
                        return true;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Get test details for JSON parsing
        /// </summary>
        public async Task<List<PushTestDetails>> GetTestDetailsForParsingAsync()
        {
            try
            {
                var testDetails = new List<PushTestDetails>();

                using (SqlConnection conn = new SqlConnection(_connectionString))
                using (SqlCommand cmd = new SqlCommand("STP_LMS_BAL_TST_DET_INF_RD", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 60; // avoid infinite waits

                    await conn.OpenAsync();

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync(CommandBehavior.CloseConnection))
                    {
                        int recordCount = 0;
                        int errorCount = 0;

                        do
                        {
                            while (await reader.ReadAsync())
                            {
                                recordCount++;
                                try
                                {
                                    var testDetail = new PushTestDetails
                                    {
                                        Id = reader.IsDBNull(reader.GetOrdinal("Id"))
                                            ? 0
                                            : Convert.ToInt32(reader.GetValue(reader.GetOrdinal("Id"))),

                                        DocumentNo = reader.IsDBNull(reader.GetOrdinal("DocumentNo"))
                                            ? string.Empty
                                            : Convert.ToString(reader.GetValue(reader.GetOrdinal("DocumentNo"))),

                                        CreatedOn = reader.IsDBNull(reader.GetOrdinal("CreatedOn"))
                                            ? DateTime.Now
                                            : Convert.ToDateTime(reader.GetValue(reader.GetOrdinal("CreatedOn"))),

                                        TestJson = reader.IsDBNull(reader.GetOrdinal("TestJson"))
                                            ? string.Empty
                                            : Convert.ToString(reader.GetValue(reader.GetOrdinal("TestJson"))),

                                        Status = reader.IsDBNull(reader.GetOrdinal("Status"))
                                            ? 0
                                            : Convert.ToInt32(reader.GetValue(reader.GetOrdinal("Status")))
                                    };

                                    testDetails.Add(testDetail);
                                }
                                catch (Exception recordEx)
                                {
                                    errorCount++;
                                    Console.WriteLine(string.Format("Record {0} failed: {1}", recordCount, recordEx.Message));
                                    continue; // Skip bad record but keep processing
                                }
                            }

                        }
                        while (await reader.NextResultAsync()); // handle multiple SELECTs in SP

                        if (errorCount > 0)
                        {
                            Console.WriteLine(string.Format("Processed {0} rows. {1} succeeded, {2} failed.", recordCount, testDetails.Count, errorCount));
                        }
                    }
                }

                return testDetails;

            }
            catch (Exception ex)
            {
                // Log the actual error for debugging
                System.Diagnostics.Debug.WriteLine(string.Format("Error in GetTestDetailsForParsingAsync: {0}", ex.Message));
                throw; // Re-throw to see the actual error
            }
        }

        /// <summary>
        /// Get product specification details
        /// </summary>
        public async Task<PrdSpecModel> GetProductSpecificationAsync(string productCode, string sampleType)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("LMS_BAL_TST_GET_SPEC_ID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ProductCode", productCode);
                        cmd.Parameters.AddWithValue("@SampleType", sampleType);

                        conn.Open();
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new PrdSpecModel
                                {
                                    SPecificationId = reader.GetInt32(reader.GetOrdinal("SPecificationId")),
                                    ProductId = reader.GetInt32(reader.GetOrdinal("ProductId"))
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Insert test model data
        /// </summary>
        public async Task<bool> InsertTestModelAsync(object testModel, object outParams)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                using (SqlCommand cmd = new SqlCommand("STP_LMS_BAL_TST_TRN_CLD", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    // 1️⃣ Add input parameters (from testModel)
                    foreach (var prop in testModel.GetType().GetProperties())
                    {
                        string paramName = "@" + prop.Name;
                        object value = prop.GetValue(testModel) ?? DBNull.Value;
                        cmd.Parameters.AddWithValue(paramName, value);
                    }

                    // 2️⃣ Add output parameters (from outParams)
                    foreach (var prop in outParams.GetType().GetProperties())
                    {
                        string paramName = "@" + prop.Name;

                        var sqlParam = new SqlParameter(paramName, DBNull.Value)
                        {
                            Direction = ParameterDirection.Output
                        };

                        // Infer SQL type if possible
                        Type propType = prop.PropertyType;
                        if (propType == typeof(int)) sqlParam.SqlDbType = SqlDbType.Int;
                        else if (propType == typeof(string)) sqlParam.SqlDbType = SqlDbType.NVarChar;
                        else if (propType == typeof(DateTime)) sqlParam.SqlDbType = SqlDbType.DateTime;
                        else if (propType == typeof(byte)) sqlParam.SqlDbType = SqlDbType.TinyInt;
                        else sqlParam.SqlDbType = SqlDbType.Variant;

                        cmd.Parameters.Add(sqlParam);
                    }

                    await conn.OpenAsync();
                    await cmd.ExecuteNonQueryAsync();

                    // 3️⃣ Assign output values back into outParams object
                    foreach (var prop in outParams.GetType().GetProperties())
                    {
                        string paramName = "@" + prop.Name;
                        if (cmd.Parameters.Contains(paramName))
                        {
                            object dbValue = cmd.Parameters[paramName].Value;
                            prop.SetValue(outParams, dbValue == DBNull.Value ? null : dbValue);
                        }
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Insert failed: " + ex.Message);
                return false;
            }
        }


        /// <summary>
        /// Get worksheet details for JSON parsing
        /// </summary>
        public async Task<List<PushWorkSheetDetails>> GetWorksheetDetailsForParsingAsync()
        {
            try
            {
                var worksheetDetails = new List<PushWorkSheetDetails>();
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM LMS_BAL_TST_DET_INF_WST WHERE LMS_BAL_TST_DET_INF_STATUS = 0 AND LMS_BAL_TST_DET_INF_JSON IS NOT NULL", conn))
                    {
                        cmd.CommandType = CommandType.Text;

                        conn.Open();
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            int recordCount = 0;
                            int errorCount = 0;

                            while (await reader.ReadAsync())
                            {
                                recordCount++;
                                try
                                {
                                    var worksheetDetail = new PushWorkSheetDetails();

                                    // Safe conversion for each field with individual error handling
                                    try
                                    {
                                        worksheetDetail.TestJson = reader.IsDBNull(reader.GetOrdinal("LMS_BAL_TST_DET_INF_JSON")) ? string.Empty : reader.GetString(reader.GetOrdinal("LMS_BAL_TST_DET_INF_JSON"));
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading TestJson: {1}", recordCount, ex.Message));
                                        worksheetDetail.TestJson = string.Empty;
                                    }

                                    try
                                    {
                                        worksheetDetail.CreatedOn = reader.IsDBNull(reader.GetOrdinal("LMS_BAL_TST_DET_INF_CRT_ON")) ? DateTime.MinValue : reader.GetDateTime(reader.GetOrdinal("LMS_BAL_TST_DET_INF_CRT_ON"));
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading CreatedOn: {1}", recordCount, ex.Message));
                                        worksheetDetail.CreatedOn = DateTime.MinValue;
                                    }

                                    try
                                    {
                                        worksheetDetail.DocumentNo = reader.IsDBNull(reader.GetOrdinal("LMS_BAL_TST_DET_DOC_NO")) ? string.Empty : reader.GetString(reader.GetOrdinal("LMS_BAL_TST_DET_DOC_NO"));
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading DocumentNo: {1}", recordCount, ex.Message));
                                        worksheetDetail.DocumentNo = string.Empty;
                                    }

                                    try
                                    {
                                        worksheetDetail.Status = reader.IsDBNull(reader.GetOrdinal("LMS_BAL_TST_DET_INF_STATUS")) ? 0 : reader.GetInt32(reader.GetOrdinal("LMS_BAL_TST_DET_INF_STATUS"));
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading Status: {1}", recordCount, ex.Message));
                                        worksheetDetail.Status = 0;
                                    }

                                    try
                                    {
                                        worksheetDetail.ISMOA = 0;
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading ISMOA: {1}", recordCount, ex.Message));
                                        worksheetDetail.ISMOA = 0;
                                    }

                                    try
                                    {
                                        worksheetDetail.PublishedDate = reader.IsDBNull(reader.GetOrdinal("LMS_BAL_TST_DET_INF_PUB_ON")) ? DateTime.MinValue : reader.GetDateTime(reader.GetOrdinal("LMS_BAL_TST_DET_INF_PUB_ON"));
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading PublishedDate: {1}", recordCount, ex.Message));
                                        worksheetDetail.PublishedDate = DateTime.MinValue;
                                    }

                                    try
                                    {
                                        worksheetDetail.JsonId = reader.IsDBNull(reader.GetOrdinal("LMS_BAL_TST_DET_INF_JSON_ID")) ? string.Empty : reader.GetString(reader.GetOrdinal("LMS_BAL_TST_DET_INF_JSON_ID"));
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Error reading JsonId: {1}", recordCount, ex.Message));
                                        worksheetDetail.JsonId = string.Empty;
                                    }

                                    worksheetDetails.Add(worksheetDetail);
                                }
                                catch (Exception recordEx)
                                {
                                    errorCount++;
                                    System.Diagnostics.Debug.WriteLine(string.Format("Worksheet Record {0} - Failed to process entire record: {1}", recordCount, recordEx.Message));
                                    // Continue to next record - don't break the loop
                                    continue;
                                }
                            }

                            // Only log if there were errors
                            if (errorCount > 0)
                            {
                                System.Diagnostics.Debug.WriteLine(string.Format("Processed {0} worksheet records total. {1} successful, {2} errors.", recordCount, worksheetDetails.Count, errorCount));
                            }
                        }
                    }
                }

                return worksheetDetails;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("Error in GetWorksheetDetailsForParsingAsync: {0}", ex.Message));
                return new List<PushWorkSheetDetails>();
            }
        }

        /// <summary>
        /// Get worksheet field count
        /// </summary>
        public async Task<string> GetWorksheetFieldCountAsync(int plantId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("STP_LMS_BAL_WST_E_FLD_CNT", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PlantID", plantId);

                        conn.Open();
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                            }
                        }
                    }
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("Error in GetWorksheetFieldCountAsync: {0}", ex.Message));
                return string.Empty;
            }
        }

        /// <summary>
        /// Get worksheet unique sequence number
        /// </summary>
        public async Task<string> GetWorksheetUniqueSeqAsync()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("STP_LMS_BAL_WST_UNID_RD", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        conn.Open();
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                            }
                        }
                    }
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("Error in GetWorksheetUniqueSeqAsync: {0}", ex.Message));
                return string.Empty;
            }
        }

        /// <summary>
        /// Update worksheet status
        /// </summary>
        public async Task<bool> UpdateWorksheetStatusAsync(string documentNo, string jsonId, int status)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("UPDATE LMS_BAL_TST_DET_INF_WST SET Status = @Status WHERE DocumentNo = @DocumentNo AND JsonId = @JsonId", conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@Status", status);
                        cmd.Parameters.AddWithValue("@DocumentNo", documentNo);
                        cmd.Parameters.AddWithValue("@JsonId", jsonId);

                        conn.Open();
                        await cmd.ExecuteNonQueryAsync();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("Error in UpdateWorksheetStatusAsync: {0}", ex.Message));
                return false;
            }
        }

        /// <summary>
        /// TrnCldFinalSubmission method
        /// </summary>
        public async Task<bool> TrnCldFinalSubmissionAsync(int plantId, int param1, int param2, int param3, int param4, int param5, int param6, int param7, string guidParam, WorkSheetModel worksheetModel)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("STP_LMS_BAL_TST_TRN_CLD", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters based on the WorkSheetModel
                        cmd.Parameters.AddWithValue("@PlantId", plantId);
                        cmd.Parameters.AddWithValue("@WorkSheetCode", worksheetModel.WorkSheetCode ?? string.Empty);
                        cmd.Parameters.AddWithValue("@WorkSheetDesc", worksheetModel.WorkSheetDesc ?? string.Empty);
                        cmd.Parameters.AddWithValue("@htmdata", worksheetModel.htmdata ?? string.Empty);
                        cmd.Parameters.AddWithValue("@UniqueSeqNO", worksheetModel.UniqueSeqNO ?? string.Empty);
                        cmd.Parameters.AddWithValue("@TpltId", worksheetModel.TpltId ?? string.Empty);
                        cmd.Parameters.AddWithValue("@DocumentNo", worksheetModel.DocumentNo ?? string.Empty);                    
                        cmd.Parameters.AddWithValue("@SecWrkShtType", worksheetModel.SecWrkShtType);
                        cmd.Parameters.AddWithValue("@GuidParam", guidParam);

                        conn.Open();
                        await cmd.ExecuteNonQueryAsync();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("Error in TrnCldFinalSubmissionAsync: {0}", ex.Message));
                return false;
            }
        }

        public async Task<int> writeData(object inputParams, string CommandText, CommandType CommandType, object OutPutParams)
        {
            int rowcount = 0;
            var @params = new DynamicParameters();
            @params.AddDynamicParams(inputParams);

            if (OutPutParams != null)
            {
                foreach (var prop in OutPutParams.GetType().GetProperties())
                {
                    @params.Add(prop.Name, prop.GetValue(OutPutParams, null), direction: ParameterDirection.Output);
                }
            }
            try
            {
                using (SqlConnection conn = new SqlConnection(_connectionString))
                {
                    await conn.OpenAsync();

                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            rowcount = await conn.ExecuteAsync(CommandText, @params, transaction, commandType: CommandType);
                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle or log the exception
            }

            await Task.Delay(10);
            return rowcount; // Return the actual row count
        }

        //public async Task<List<T>> ReadList<T>(object Inputparams, string commandText, CommandType commandType)
        //{
        //    try
        //    {
        //        SqlConnection connection = new SqlConnection(_connectionString);
        //        connection.Open();
        //        var results = await connection.QueryAsync<T>(commandText, Inputparams, commandType: commandType);
        //        return results.ToList<T>();
        //    }
        //    catch (Exception ex)
        //    {
        //        ExceptionMessage = ex.Message;
        //        throw;
        //    }
        //}

        //public async Task<T> ReadData<T>(object Inputparams, string commandText, CommandType commandType)
        //{
        //    try
        //    {
        //        SqlConnection connection = new SqlConnection(_connectionString);
        //        await connection.OpenAsync();

        //        var results = (await connection.QueryAsync<T>(commandText, Inputparams, commandType: commandType)).FirstOrDefault();
        //        return results;
        //    }
        //    catch (Exception ex)
        //    {
        //        ExceptionMessage = ex.Message;
        //        throw;
        //    }
        //}

    }
}
