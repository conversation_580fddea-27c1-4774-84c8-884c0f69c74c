using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CommonHttpClient
{
    public class ResponseDetails
    {
        public bool IsSuccess { get; set; }
        public string Response { get; set; }
        public int? StatusCode { get; set; }
        public string Message { get; set; }
    }

    public class DraftTestDetail
    {
        [JsonProperty("TestName")]
        public string TestName { get; set; }

        [JsonProperty("Procedure")]
        public string Procedure { get; set; }

        [JsonProperty("ISMOA")]
        public int ISMOA { get; set; }

        [JsonProperty("SpecificationId")]
        public string SpecNo { get; set; }
    }

    public class FileTestDto
    {
        [JsonProperty("documentName")]
        public string DocumentNo { get; set; }

        [JsonProperty("craetedOn")]
        public string CreatedOn { get; set; }

        [JsonProperty("json")]
        public string TestJson { get; set; }

        [JsonProperty("lastModificationTime")]
        public string PublishedDate { get; set; }
    }

    public class FileworksheetDto
    {
        [JsonProperty("documentName")]
        public string DocumentNo { get; set; }

        [JsonProperty("creationTime")]
        public string CreatedOn { get; set; }

        [JsonProperty("json")]
        public string TestJson { get; set; }

        [JsonProperty("publishedOn")]
        public string PublishedDate { get; set; }

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("worksheetType")]
        public string worksheetType { get; set; }
    }

    public class PushworksheetRequest
    {
        public string DocumentNo { get; set; }
        public string CreatedOn { get; set; }
        public string TestJson { get; set; }      
        public string PublishedDate { get; set; }
        public string JsonId { get; set; }
    }

    public class CaligenieWorksheetResponse
    {
        [JsonProperty("totalCount")]
        public int totalCount { get; set; }

        [JsonProperty("items")]
        public List<FileworksheetDto> items { get; set; }
    }

    public class ImageApiResponse
    {
        [JsonProperty("testName")]
        public string testname { get; set; }

        [JsonProperty("base64Image")]
        public string Image { get; set; }
    }

    public class MyData
    {
        public string documentName { get; set; }
        public string json { get; set; }
        public bool IsDeleted { get; set; }
        public string DeleterId { get; set; }
        public DateTime? DeletionTime { get; set; } // Nullable DateTime
        public DateTime? LastModificationTime { get; set; }
        public string LastModifierId { get; set; }
        public DateTime CreationTime { get; set; }
        public string CreatorId { get; set; }
        public string Id { get; set; }
    }

    public class TestCLDModel
    {
        public int TestParsedChildId { get; set; } = 0;
        public string TestCode { get; set; } = "";
        public string TestDesc { get; set; } = "";
        public string TransCode { get; set; } = "";
        public int ProductId { get; set; } = 0;
        public int SpecificationId { get; set; } = 0;
        public int WorkSheetId { get; set; } = 0;
        public int TestCategoryId { get; set; } = 0;
        public int TchTypeId { get; set; } = 0;
        public string Description { get; set; } = "";
        public string MethodNo { get; set; } = "";
        public string MethodNoList { get; set; } = "";
        public string LodLimits { get; set; } = "";
        public string LoqLimits { get; set; } = "";
        public string AlertLimits { get; set; } = "";
        public string ActionLimits { get; set; } = "";

        public string LODCOATxt { get; set; } = "";
        public string LOQCOATxt { get; set; } = "";
        public string LOAlertCOATxt { get; set; } = "";
        public string LOActionCOATxt { get; set; } = "";

        public string LODDecimals { get; set; } = "";
        public int TestType { get; set; } = 0;
        public string Variables { get; set; } = "";
        public string Symbols { get; set; } = "";
        public string Units { get; set; } = "";
        public string Formula { get; set; } = "";
        public string FormulaSymbols { get; set; } = "";
        public string FormulaVariables { get; set; } = "";
        public string FormulaUnits { get; set; } = "";
        public string PassLimits { get; set; } = "";
        public string SubTests { get; set; } = "";
        public string Readings { get; set; } = "";
        public string ReadingUnits { get; set; } = "";
        public byte IsMax { get; set; } = 0;
        public string MaxLimit { get; set; } = "";
        public byte IsMin { get; set; } = 0;
        public string MinLimit { get; set; } = "";
        public byte IsAvg { get; set; } = 0;
        public string AvgLLimit { get; set; } = "";
        public string AvgULimit { get; set; } = "";
        public string NMTLimit { get; set; } = "";
        public string NMT_ValueLimit { get; set; } = "";
        public byte IsSD { get; set; } = 0;
        public string SDLimit { get; set; } = "";
        public byte IsRSD { get; set; } = 0;
        public string RSDLimit { get; set; } = "";
        public string LabelClaim { get; set; } = "";
        public string LCMinLimit { get; set; } = "";
        public string LCMaxLimit { get; set; } = "";
        public string LCDevAvgLimit { get; set; } = "";
        public string NoOfDec { get; set; } = "";
        public int NumberOfReadings { get; set; } = 0;
        public string NoOfDecList { get; set; } = "";
        public int IsWos { get; set; } = 0;
        public string SP_Variables { get; set; } = "";
        public string SP_Symbols { get; set; } = "";
        public string SP_Units { get; set; } = "";
        public string SP_Types { get; set; } = "";
        public string SP_InputExp { get; set; } = "";
        public string SP_NoOfDec { get; set; } = "";
        public string SP_LL { get; set; } = "";
        public string SP_UL { get; set; } = "";
        public string SP_COA { get; set; } = "";
        public string Txt_COA { get; set; } = "";
        public int StndAnalTme { get; set; } = 0;
        public string Comments { get; set; } = "";
        public string LoqConds { get; set; } = "";
        public string LodConds { get; set; } = "";
        public byte MinDec { get; set; } = 0;
        public byte MaxDec { get; set; } = 0;
        public byte AvgDec { get; set; } = 0;
        public byte SDDec { get; set; } = 0;
        public byte RSDDec { get; set; } = 0;
        public byte RedReqChk { get; set; } = 0;
        public int TestCldId { get; set; } = 0;

        public int ConfigID { get; set; } = 0;
        public byte PlantID { get; set; } = 0;
        public int EsignerId { get; set; } = 1;
        public float EsignerRank { get; set; } = 0;
        public int EsignerRoleId { get; set; } = 0;
        public string Esign { get; set; } = "";
        public DateTime? EsignDateTime { get; set; } = DateTime.Today;
        public string EsignerRem { get; set; } = "--";

        public int AulId { get; set; } = 0;
        public byte NewStatus { get; set; } = 0;
        public byte OldStatus { get; set; } = 0;
        public byte TransType { get; set; } = 0;
        public int NewOws { get; set; } = 0;
        public int DocId { get; set; } = 0;
    }

    public class Root
    {
        [JsonProperty("tests")]
        public List<Test> Tests { get; set; }
    }

    public class Test
    {
        [JsonProperty("Test Name")]
        public string TestName { get; set; }

        [JsonProperty("Test Type")]
        public string TestType { get; set; }

        [JsonProperty("Number of Variables")]
        public int NumberOfVariables { get; set; }

        public List<Variable> Variables { get; set; }

        [JsonProperty("Sub Tests")]
        public List<subtest> subtests { get; set; }

        [JsonProperty("Special Variables")]
        public List<SPVariable> SpecialVariables { get; set; }

        [JsonProperty("Pass Limit Description")]
        public string PassLimitDescription { get; set; }

        [JsonProperty("Specification description")]
        public string SpecificationDescription { get; set; }

        public string Formula { get; set; }

        [JsonProperty("Pass Limits")]
        public string PassLimits { get; set; }

        [JsonProperty("Limit of Detection")]
        public string LimitOfDetection { get; set; }

        [JsonProperty("Limit of Quantification")]
        public string LimitOfQuantification { get; set; }

        [JsonProperty("Number of Decimals for LOD/LOQ")]
        public string NumberOfDecimalsForLODLOQ { get; set; }

        [JsonProperty("Limit of Alert")]
        public string LimitOfAlert { get; set; }

        [JsonProperty("Limit of Action")]
        public string LimitOfAction { get; set; }

        [JsonProperty("UOM")]
        public string UOM { get; set; }

        [JsonProperty("No. Of Decimals")]
        public string NoOfDecimals { get; set; }

        [JsonProperty("Method No")]
        public string MethodNo { get; set; }

        [JsonProperty("Number of Readings")]
        public string NumberofReadings { get; set; }

        [JsonProperty("Description Of Reading")]
        public string DescriptionOfReading { get; set; }

        [JsonProperty("Calculations")]
        public List<Calculations> Calculations { get; set; }

        [JsonProperty("Qualities")]
        public string Qualities { get; set; }

        [JsonProperty("Final Result Description")]
        public string FinalResultDescription { get; set; }

        [JsonProperty("Additional Parameters")]
        public List<AdditionalParameters> AdditionalParameters { get; set; }

    }

    public class Variable
    {
        [JsonProperty("S.No")]
        public int? SerialNumber { get; set; }

        [JsonProperty("Variable Name")]
        public string VariableName { get; set; }

        public string Symbol { get; set; }

        [JsonProperty("UOM")]
        public string UnitOfMeasurement { get; set; }

        [JsonProperty("Quality Label")]
        public string QualityLabel { get; set; }

        public string Description { get; set; }

        [JsonProperty("Pass Status")]
        public string PassStatus { get; set; }
    }

    public class subtest
    {
        [JsonProperty("Sub Test Name")]
        public string SubTestName { get; set; }

        [JsonProperty("Method No")]
        public string MethodNo { get; set; }

        [JsonProperty("Formula")]
        public string Formula { get; set; }

        [JsonProperty("UOM")]
        public string UOM { get; set; }

        [JsonProperty("Pass Limit Description")]
        public string PassLimitDescription { get; set; }

        [JsonProperty("Specification description")]
        public string SpecificationDescription { get; set; }

        [JsonProperty("Pass Limits")]
        public string PassLimits { get; set; }

        [JsonProperty("Limit of Detection")]
        public string LimitOfDetection { get; set; }

        [JsonProperty("Limit of Quantification")]
        public string LimitOfQuantification { get; set; }

        [JsonProperty("No. Of Decimals for LOD/LOQ")]
        public string NumberOfDecimalsForLODLOQ { get; set; }

        [JsonProperty("Limit of Alert")]
        public string LimitOfAlert { get; set; }

        [JsonProperty("Limit of Action")]
        public string LimitOfAction { get; set; }

        [JsonProperty("No. Of Decimals")]
        public string NoOfDecimals { get; set; }

        [JsonProperty("Qualities")]
        public List<Dictionary<string, string>> Qualities { get; set; }

        [JsonProperty("Pass Status")]
        public List<Dictionary<string, string>> PassStatus { get; set; }

        public List<Variable> Variables { get; set; }
    }

    public class SPVariable
    {
        [JsonProperty("Variable Name")]
        public string VariableName { get; set; }

        [JsonProperty("Symbol")]
        public string Symbol { get; set; }

        [JsonProperty("UOM")]
        public string UOM { get; set; }

        [JsonProperty("Input Expression")]
        public string Formula { get; set; }

        [JsonProperty("Lower Limit")]
        public string LowerLimit { get; set; }

        [JsonProperty("Upper Limit")]
        public string UpperLimit { get; set; }

        [JsonProperty("No. Of Decimals")]
        public string Decimals { get; set; }

        [JsonProperty("Display in COA")]
        public string DisplayinCOA { get; set; }

        [JsonProperty("Function Type")]
        public string FunctionType { get; set; }

    }

    public class Calculations
    {
        [JsonProperty("Calculation")]
        public string Calculation { get; set; }

        [JsonProperty("Display")]
        public string Display { get; set; }

        [JsonProperty("Limit")]
        public string Limit { get; set; }

        [JsonProperty("Average Limits")]
        public List<AvgLimits> AvgLimits { get; set; }
    }

    public class AvgLimits
    {
        [JsonProperty("Upper Limit")]
        public string ULimit { get; set; }

        [JsonProperty("Lower Limit")]
        public string LLimit { get; set; }
    }

    public class AdditionalParameters
    {
        [JsonProperty("NMT")]
        public string NMT { get; set; }

        [JsonProperty("NRSHD")]
        public string NRSHD { get; set; }

        [JsonProperty("Label Claim")]
        public string LabelClaim { get; set; }

        [JsonProperty("MNVLC")]
        public string MNVLC { get; set; }

        [JsonProperty("MXVLC")]
        public string MXVLC { get; set; }

        [JsonProperty("Pass Limit Description")]
        public string PassLimitDescription { get; set; }

    }

    public class PushTestDetails
    {
        public int Id { get; set; }
        public string DocumentNo { get; set; }
        public DateTime CreatedOn { get; set; }
        public string TestJson { get; set; }
        public int Status { get; set; }
    }

    public class PrdSpecModel
    {
        public int ProductId { get; set; }
        public int SPecificationId { get; set; }
    }

    public class ProductTypeDetails
    {
        public string Product { get; set; }
        public string ProductCode { get; set; }
        public string MaterialType { get; set; }
    }

    public class ProductDetails
    {
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string MaterialType { get; set; }
    }

    public class PushWSDetailsDto
    {
        public string TestJson { get; set; }
        public DateTime CreatedOn { get; set; }
        public string DocumentNo { get; set; }
        public int ISMOA { get; set; }
        public DateTime PublishedDate { get; set; }
        public string JsonId { get; set; }
    }

    public class PushWorkSheetDetails
    {
        public string TestJson { get; set; }
        public DateTime CreatedOn { get; set; }
        public string DocumentNo { get; set; }
        public int Status { get; set; }
        public int ISMOA { get; set; }
        public DateTime PublishedDate { get; set; }
        public string JsonId { get; set; }
    }

    public class DraftTestDetailWithWSTypes
    {
        [JsonProperty("TestName")]
        public string TestName { get; set; }

        [JsonProperty("WSTypes")]
        public List<WSType> WSTypes { get; set; }

        [JsonProperty("Images")]
        public List<object> Images { get; set; }

        [JsonProperty("SpecificationId")]
        public string SpecNo { get; set; }
    }

    public class WSType
    {
        [JsonProperty("Type")]
        public string Type { get; set; }

        [JsonProperty("SecName")]
        public string SecName { get; set; }

        [JsonProperty("Procedure")]
        public string Procedure { get; set; }
    }

    public class WorkSheetModel
    {
        public string WorkSheetCode { get; set; }
        public string WorkSheetDesc { get; set; }
        public string WorkSheetFieldListId { get; set; }
        public string WorkSheetFieldListCode { get; set; }
        public string WorkSheetFieldListBnd { get; set; }
        public string ProdCode { get; set; }
        public int ProdId { get; set; }
        public string htmdata { get; set; }
        public string TDSFldCntLst { get; set; }
        public string UniqueSeqNO { get; set; }
        public string AUTOWSFieldIDList { get; set; }
        public string AUTOWsFieldNmeList { get; set; }
        public string AUTOWsFieldBndList { get; set; }
        public int SectionCategory { get; set; }
        public int SecWrkShtType { get; set; }
        public object SectionsList { get; set; }
        public string TpltId { get; set; }
        public string BFLDSYM { get; set; }
        public string BFLDIDLST { get; set; }
        public int WSTDraftId { get; set; }
        public string DocumentNo { get; set; }
        public bool ISMOA { get; set; }
    }

    public class OutParams
    {
        public int ReturnStatus { get; set; } = 0;
        public string KeyFeild { get; set; } = string.Empty;
    }
    public class WorksheetModel1
    {
        public string WorksheetName { get; set; } = "";
        public string htmdata { get; set; } = "";
        public string JsonId { get; set; } = "";
        public int type { get; set; }
       
    }
}
