﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Dapper" version="2.1.66" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.1" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.Http" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.Logging" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.Options" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="Microsoft.Extensions.Primitives" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" requireReinstallation="true" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" requireReinstallation="true" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" requireReinstallation="true" />
</packages>